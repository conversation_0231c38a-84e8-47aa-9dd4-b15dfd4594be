// Service Worker for ViralDeals.online
const CACHE_NAME = 'viraldeals-v1';
const API_CACHE_NAME = 'viraldeals-api-v1';
const IMAGE_CACHE_NAME = 'viraldeals-images-v1';

// Assets to cache immediately
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/manifest.json',
  '/placeholder-image.jpg',
  // Add other static assets as needed
];

// API endpoints to cache
const API_ENDPOINTS = [
  '/api/products/featured',
  '/api/products/categories',
  '/api/products',
];

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('Service Worker installing...');
  
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        // Skip waiting to activate immediately
        return self.skipWaiting();
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker activating...');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== CACHE_NAME && 
                cacheName !== API_CACHE_NAME && 
                cacheName !== IMAGE_CACHE_NAME) {
              console.log('Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        // Take control of all pages immediately
        return self.clients.claim();
      })
  );
});

// Fetch event - implement caching strategies
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }

  // Handle different types of requests
  if (url.pathname.startsWith('/api/')) {
    // API requests - Network First with fallback to cache
    event.respondWith(handleApiRequest(request));
  } else if (isImageRequest(request)) {
    // Image requests - Cache First
    event.respondWith(handleImageRequest(request));
  } else {
    // Static assets - Cache First with network fallback
    event.respondWith(handleStaticRequest(request));
  }
});

// Handle API requests - Network First strategy
async function handleApiRequest(request) {
  const url = new URL(request.url);
  
  try {
    // Try network first
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      // Cache successful responses for specific endpoints
      if (shouldCacheApiResponse(url.pathname)) {
        const cache = await caches.open(API_CACHE_NAME);
        cache.put(request, networkResponse.clone());
      }
      return networkResponse;
    }
    
    // If network fails, try cache
    return await getCachedResponse(request, API_CACHE_NAME);
  } catch (error) {
    console.log('Network failed, trying cache for:', request.url);
    return await getCachedResponse(request, API_CACHE_NAME);
  }
}

// Handle image requests - Cache First strategy
async function handleImageRequest(request) {
  try {
    // Try cache first
    const cachedResponse = await getCachedResponse(request, IMAGE_CACHE_NAME);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // If not in cache, fetch from network
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      // Cache the image
      const cache = await caches.open(IMAGE_CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.log('Failed to load image:', request.url);
    // Return placeholder image if available
    return await getCachedResponse(new Request('/placeholder-image.jpg'), CACHE_NAME);
  }
}

// Handle static requests - Cache First strategy
async function handleStaticRequest(request) {
  try {
    // Try cache first
    const cachedResponse = await getCachedResponse(request, CACHE_NAME);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // If not in cache, fetch from network
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      // Cache the response
      const cache = await caches.open(CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.log('Failed to load static asset:', request.url);
    
    // For navigation requests, return cached index.html
    if (request.mode === 'navigate') {
      return await getCachedResponse(new Request('/index.html'), CACHE_NAME);
    }
    
    throw error;
  }
}

// Helper functions
async function getCachedResponse(request, cacheName) {
  const cache = await caches.open(cacheName);
  return await cache.match(request);
}

function isImageRequest(request) {
  return request.destination === 'image' || 
         /\.(jpg|jpeg|png|gif|webp|svg)$/i.test(new URL(request.url).pathname);
}

function shouldCacheApiResponse(pathname) {
  return API_ENDPOINTS.some(endpoint => pathname.startsWith(endpoint)) ||
         pathname.includes('/products/featured') ||
         pathname.includes('/products/categories');
}

// Background sync for offline actions
self.addEventListener('sync', (event) => {
  console.log('Background sync triggered:', event.tag);
  
  if (event.tag === 'cart-sync') {
    event.waitUntil(syncCartData());
  }
});

// Sync cart data when back online
async function syncCartData() {
  try {
    // Get pending cart actions from IndexedDB
    const pendingActions = await getPendingCartActions();
    
    for (const action of pendingActions) {
      try {
        await fetch('/api/cart', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(action),
        });
        
        // Remove from pending actions
        await removePendingCartAction(action.id);
      } catch (error) {
        console.error('Failed to sync cart action:', error);
      }
    }
  } catch (error) {
    console.error('Cart sync failed:', error);
  }
}

// Placeholder functions for IndexedDB operations
async function getPendingCartActions() {
  // Implement IndexedDB read operation
  return [];
}

async function removePendingCartAction(id) {
  // Implement IndexedDB delete operation
}

// Handle push notifications (for future implementation)
self.addEventListener('push', (event) => {
  if (event.data) {
    const data = event.data.json();
    
    const options = {
      body: data.body,
      icon: '/icon-192x192.png',
      badge: '/badge-72x72.png',
      tag: data.tag || 'default',
      data: data.data || {},
    };
    
    event.waitUntil(
      self.registration.showNotification(data.title, options)
    );
  }
});

// Handle notification clicks
self.addEventListener('notificationclick', (event) => {
  event.notification.close();
  
  const data = event.notification.data;
  const url = data.url || '/';
  
  event.waitUntil(
    clients.openWindow(url)
  );
});

console.log('Service Worker loaded');
