export declare const REPLACEMENT_CHARACTER = "\uFFFD";
export declare enum CODE_POINTS {
    EOF = -1,
    NULL = 0,
    TABULATION = 9,
    CARRIAGE_RETURN = 13,
    LINE_FEED = 10,
    FORM_FEED = 12,
    SPACE = 32,
    EXCLAMATION_MARK = 33,
    QUOTATION_MARK = 34,
    AMPERSAND = 38,
    APOSTROPHE = 39,
    HYPHEN_MINUS = 45,
    SOLIDUS = 47,
    DIGIT_0 = 48,
    DIGIT_9 = 57,
    SEMICOLON = 59,
    LESS_THAN_SIGN = 60,
    EQUALS_SIGN = 61,
    GREATER_THAN_SIGN = 62,
    QUESTION_MARK = 63,
    LATIN_CAPITAL_A = 65,
    LATIN_CAPITAL_Z = 90,
    RIGHT_SQUARE_BRACKET = 93,
    GRAVE_ACCENT = 96,
    LATIN_SMALL_A = 97,
    LATIN_SMALL_Z = 122
}
export declare const SEQUENCES: {
    DASH_DASH: string;
    CDATA_START: string;
    DOCTYPE: string;
    SCRIPT: string;
    PUBLIC: string;
    SYSTEM: string;
};
export declare function isSurrogate(cp: number): boolean;
export declare function isSurrogatePair(cp: number): boolean;
export declare function getSurrogatePairCodePoint(cp1: number, cp2: number): number;
export declare function isControlCodePoint(cp: number): boolean;
export declare function isUndefinedCodePoint(cp: number): boolean;
