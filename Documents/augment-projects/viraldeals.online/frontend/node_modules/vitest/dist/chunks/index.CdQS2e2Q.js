import { c as createExpect, a as globalExpect, i as inject, v as vi, b as vitest } from './vi.bdSIJ99Y.js';
import { b as bench } from './benchmark.CYdenmiT.js';
import { expectTypeOf } from 'expect-type';
import { afterAll, afterEach, beforeAll, beforeEach, describe, it, onTestFailed, onTestFinished, suite, test } from '@vitest/runner';
import * as chai from 'chai';
import { assert, should } from 'chai';

const assertType = function assertType() {};

// TODO: deprecate <reference types="vitest" /> in favor of `<reference types="vitest/config" />`

var VitestIndex = /*#__PURE__*/Object.freeze({
  __proto__: null,
  afterAll: afterAll,
  afterEach: afterEach,
  assert: assert,
  assertType: assertType,
  beforeAll: beforeAll,
  beforeEach: beforeEach,
  bench: bench,
  chai: chai,
  createExpect: createExpect,
  describe: describe,
  expect: globalExpect,
  expectTypeOf: expectTypeOf,
  inject: inject,
  it: it,
  onTestFailed: onTestFailed,
  onTestFinished: onTestFinished,
  should: should,
  suite: suite,
  test: test,
  vi: vi,
  vitest: vitest
});

export { VitestIndex as V, assertType as a };
