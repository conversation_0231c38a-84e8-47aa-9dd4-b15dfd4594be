import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';
import User from './models/User.js';
import dotenv from 'dotenv';

dotenv.config();

const createAdminUser = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/viraldeals');
    console.log('Connected to MongoDB');

    // Check if admin user already exists
    const existingAdmin = await User.findOne({ email: '<EMAIL>' }).select('+password');

    if (existingAdmin) {
      console.log('Admin user already exists:', existingAdmin.email);
      console.log('Role:', existingAdmin.role);
      console.log('Password field exists:', !!existingAdmin.password);

      // Test password
      if (existingAdmin.password) {
        const testPassword = await existingAdmin.comparePassword('admin123');
        console.log('Password test result:', testPassword);

        if (!testPassword) {
          console.log('Password mismatch! Deleting and recreating admin user...');
          await User.deleteOne({ email: '<EMAIL>' });
        } else {
          process.exit(0);
        }
      } else {
        console.log('Password field is missing! Deleting and recreating admin user...');
        await User.deleteOne({ email: '<EMAIL>' });
      }
    }

    // Create admin user (password will be hashed by pre-save middleware)
    const adminUser = new User({
      name: 'Admin User',
      email: '<EMAIL>',
      password: 'admin123', // Will be hashed by pre-save middleware
      phone: '9876543210',
      role: 'admin',
      isVerified: true
    });

    await adminUser.save();
    console.log('✅ Admin user created successfully!');
    console.log('Email: <EMAIL>');
    console.log('Password: admin123');
    console.log('Role: admin');
    
  } catch (error) {
    console.error('Error creating admin user:', error);
  } finally {
    await mongoose.disconnect();
    process.exit(0);
  }
};

createAdminUser();
