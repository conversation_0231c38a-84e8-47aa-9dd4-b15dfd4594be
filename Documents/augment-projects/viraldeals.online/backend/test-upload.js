import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Test upload directory creation
const uploadsDir = path.join(__dirname, 'uploads');
const productsDir = path.join(uploadsDir, 'products');

console.log('Testing upload directory setup...');

// Check if uploads directory exists
if (!fs.existsSync(uploadsDir)) {
  console.log('❌ uploads directory does not exist');
  fs.mkdirSync(uploadsDir, { recursive: true });
  console.log('✅ Created uploads directory');
} else {
  console.log('✅ uploads directory exists');
}

// Check if products directory exists
if (!fs.existsSync(productsDir)) {
  console.log('❌ products directory does not exist');
  fs.mkdirSync(productsDir, { recursive: true });
  console.log('✅ Created products directory');
} else {
  console.log('✅ products directory exists');
}

// Check directory permissions
try {
  const testFile = path.join(productsDir, 'test-write.txt');
  fs.writeFileSync(testFile, 'test');
  fs.unlinkSync(testFile);
  console.log('✅ Directory has write permissions');
} catch (error) {
  console.log('❌ Directory write permission error:', error.message);
}

// List directory contents
console.log('\nDirectory structure:');
console.log('uploads/', fs.readdirSync(uploadsDir));
if (fs.existsSync(productsDir)) {
  console.log('uploads/products/', fs.readdirSync(productsDir));
}

console.log('\nUpload directory test completed!');
