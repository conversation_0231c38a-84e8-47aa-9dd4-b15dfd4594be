import mongoose from 'mongoose';
import User from './models/User.js';
import dotenv from 'dotenv';

dotenv.config();

const debugLogin = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/viraldeals');
    console.log('Connected to MongoDB');

    // Find the admin user
    const user = await User.findOne({ email: '<EMAIL>' }).select('+password');
    
    if (!user) {
      console.log('❌ User not found');
      return;
    }

    console.log('✅ User found:');
    console.log('   ID:', user._id);
    console.log('   Name:', user.name);
    console.log('   Email:', user.email);
    console.log('   Role:', user.role);
    console.log('   Password hash exists:', !!user.password);
    console.log('   Password hash length:', user.password ? user.password.length : 0);
    console.log('   Is verified:', user.isVerified);

    // Test password comparison
    console.log('\n🔐 Testing password comparison:');
    const testPasswords = ['admin123', 'Admin123', 'ADMIN123', 'admin', '123'];
    
    for (const testPassword of testPasswords) {
      try {
        const isMatch = await user.comparePassword(testPassword);
        console.log(`   "${testPassword}": ${isMatch ? '✅ MATCH' : '❌ NO MATCH'}`);
      } catch (error) {
        console.log(`   "${testPassword}": ❌ ERROR - ${error.message}`);
      }
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await mongoose.disconnect();
    process.exit(0);
  }
};

debugLogin();
