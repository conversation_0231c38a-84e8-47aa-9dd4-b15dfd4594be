import fs from 'fs';
import FormData from 'form-data';
import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:5001/api';

const testUploadFlow = async () => {
  try {
    console.log('🧪 Testing complete upload flow...\n');

    // Step 1: Login as admin
    console.log('1. Logging in as admin...');
    const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    });

    if (!loginResponse.ok) {
      const errorData = await loginResponse.json();
      throw new Error(`Login failed: ${errorData.message}`);
    }

    const loginData = await loginResponse.json();
    const token = loginData.token;
    console.log('✅ Login successful');
    console.log('   User:', loginData.user.name);
    console.log('   Role:', loginData.user.role);
    console.log('   Token:', token.substring(0, 20) + '...\n');

    // Step 2: Test image upload
    console.log('2. Testing image upload...');
    
    // Check if test image exists
    if (!fs.existsSync('test-image.png')) {
      throw new Error('Test image not found. Please create test-image.png first.');
    }

    const formData = new FormData();
    formData.append('image', fs.createReadStream('test-image.png'));

    const uploadResponse = await fetch(`${BASE_URL}/upload/image`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        ...formData.getHeaders()
      },
      body: formData
    });

    if (!uploadResponse.ok) {
      const errorData = await uploadResponse.json();
      throw new Error(`Upload failed: ${errorData.message}`);
    }

    const uploadData = await uploadResponse.json();
    console.log('✅ Image upload successful');
    console.log('   URL:', uploadData.url);
    console.log('   Filename:', uploadData.filename);
    console.log('   Size:', uploadData.size, 'bytes\n');

    // Step 3: Verify file exists
    console.log('3. Verifying uploaded file...');
    const uploadedFilePath = `uploads/products/${uploadData.filename}`;
    
    if (fs.existsSync(uploadedFilePath)) {
      const stats = fs.statSync(uploadedFilePath);
      console.log('✅ File exists on disk');
      console.log('   Path:', uploadedFilePath);
      console.log('   Size:', stats.size, 'bytes');
    } else {
      throw new Error('Uploaded file not found on disk');
    }

    // Step 4: Test file access via HTTP
    console.log('\n4. Testing file access via HTTP...');
    const fileUrl = `http://localhost:5001${uploadData.url}`;
    const fileResponse = await fetch(fileUrl);
    
    if (fileResponse.ok) {
      console.log('✅ File accessible via HTTP');
      console.log('   URL:', fileUrl);
      console.log('   Content-Type:', fileResponse.headers.get('content-type'));
    } else {
      throw new Error(`File not accessible via HTTP: ${fileResponse.status}`);
    }

    console.log('\n🎉 All tests passed! Image upload functionality is working correctly.');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
};

// Run the test
testUploadFlow();
